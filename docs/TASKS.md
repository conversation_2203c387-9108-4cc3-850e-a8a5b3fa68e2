# BonkAI Development Tasks

## Overview
This document outlines the concrete development tasks needed to implement the features described in the PRD. The current codebase has basic chat functionality but is missing most advanced features.

## Current Status
✅ **Implemented:**
- Basic Next.js 15 app with App Router
- NextAuth.js v5 authentication (credentials + guest)
- PostgreSQL database with Drizzle ORM
- Basic chat with xAI Grok models
- Artifact system (code, image, sheet, text)
- Message persistence and history
- Basic rate limiting (message count per day)
- shadcn/ui components

❌ **Missing (claimed as "Done" in PRD but not implemented):**
- Subscription system and tiers
- Stripe payment integration
- Telegram bot
- Redis rate limiting
- Usage tracking/token counting
- Web3/crypto payments
- Advanced AI routing
- Cross-platform sync

## Phase 1: Subscription System Foundation

### 1.1 Database Schema Updates
- [ ] Add `subscriptionTier` field to User table
- [ ] Add `stripeCustomerId` field to User table
- [ ] Create `usageLog` table for token tracking
- [ ] Create `subscription` table for Stripe data
- [ ] Add database indexes for performance
- [ ] Run migrations

### 1.2 Subscription Types Implementation
- [ ] Create `lib/types.ts` with subscription enums and interfaces
- [ ] Implement `SubscriptionTier` enum (FREE, BRONZE, SILVER, DIAMOND)
- [ ] Define `TierLimits` interface with pricing and features
- [ ] Create subscription tier configuration object

### 1.3 Stripe Integration
- [ ] Install Stripe SDK
- [ ] Create `lib/payments/stripe.ts` with Stripe client
- [ ] Implement subscription creation functions
- [ ] Create Stripe webhook handlers
- [ ] Add Stripe price IDs configuration
- [ ] Create subscription management API routes
- [ ] Build subscription upgrade/downgrade UI

## Phase 2: Advanced Authentication & Authorization

### 2.1 Middleware Enhancement
- [ ] Create subscription gating middleware
- [ ] Implement tier-based route protection
- [ ] Add usage limit checking middleware
- [ ] Create API rate limiting per tier

### 2.2 User Management
- [ ] Update user queries for subscription data
- [ ] Implement subscription status checking
- [ ] Create user dashboard for subscription management
- [ ] Add billing history functionality

## Phase 3: AI System Enhancement

### 3.1 Model Routing & Restrictions
- [ ] Create `lib/ai/models.ts` with tier-based model access
- [ ] Implement `getModelForTask` function
- [ ] Update AI providers with tier restrictions
- [ ] Add model selection based on subscription
- [ ] Implement usage tracking in chat API

### 3.2 Usage Tracking System
- [ ] Create token counting utilities
- [ ] Implement usage logging in chat routes
- [ ] Add monthly usage reset functionality
- [ ] Create usage analytics dashboard
- [ ] Add usage limit warnings

## Phase 4: Telegram Bot Integration

### 4.1 Bot Setup
- [ ] Install grammY framework
- [ ] Create `lib/telegram/bot.ts` with bot configuration
- [ ] Implement user authentication for Telegram
- [ ] Create user linking system (web ↔ Telegram)

### 4.2 Bot Commands
- [ ] Implement `/start` command with user registration
- [ ] Create `/chat [prompt]` command with tier checking
- [ ] Add `/image [prompt]` command (Silver+ only)
- [ ] Implement `/status` command for usage/tier info
- [ ] Add `/upgrade` command for subscription management

### 4.3 Cross-Platform Sync
- [ ] Design WebSocket architecture for real-time sync
- [ ] Implement WebSocket server (port 3001)
- [ ] Create message synchronization between web/Telegram
- [ ] Add real-time chat updates

## Phase 5: Redis & Performance

### 5.1 Redis Integration
- [ ] Install Redis client
- [ ] Create `lib/redis/client.ts`
- [ ] Implement sliding window rate limiting
- [ ] Add session caching with Redis
- [ ] Create usage tracking with Redis

### 5.2 Performance Optimization
- [ ] Add database connection pooling
- [ ] Implement query optimization
- [ ] Add caching for subscription data
- [ ] Optimize AI model loading

## Phase 6: Web3 Integration Foundation

### 6.1 Wallet Integration
- [ ] Install WalletConnect SDK
- [ ] Create Solana wallet connection components
- [ ] Implement wallet authentication
- [ ] Add wallet state management with Zustand

### 6.2 Crypto Payments
- [ ] Research Solana payment processing
- [ ] Create crypto payment utilities
- [ ] Implement $BONK token payment option
- [ ] Add crypto subscription management

## Phase 7: Security & Testing

### 7.1 Security Enhancements
- [ ] Implement signature verification for sensitive actions
- [ ] Add CSRF protection
- [ ] Create API key management
- [ ] Add input validation and sanitization

### 7.2 Testing
- [ ] Write Playwright tests for subscription flows
- [ ] Add unit tests for payment processing
- [ ] Create integration tests for Telegram bot
- [ ] Add load testing for AI endpoints

## Phase 8: UI/UX Improvements

### 8.1 Subscription Management UI
- [ ] Create subscription tier selection page
- [ ] Build payment form components
- [ ] Add usage dashboard
- [ ] Implement billing history page

### 8.2 Chat Enhancements
- [ ] Add model selector with tier restrictions
- [ ] Create usage indicators in chat
- [ ] Add subscription upgrade prompts
- [ ] Implement tier-based feature unlocks

## Phase 9: Deployment & Infrastructure

### 9.1 Environment Setup
- [ ] Configure production environment variables
- [ ] Set up Redis instance
- [ ] Configure Stripe webhooks
- [ ] Set up monitoring and logging

### 9.2 CI/CD
- [ ] Add automated testing pipeline
- [ ] Create deployment scripts
- [ ] Set up database migration automation
- [ ] Add health checks

## Priority Order
1. **High Priority**: Subscription system, Stripe integration, database updates
2. **Medium Priority**: Telegram bot, Redis rate limiting, usage tracking
3. **Low Priority**: Web3 integration, advanced features, optimizations

## Estimated Timeline
- Phase 1-2: 2-3 weeks
- Phase 3-4: 2-3 weeks  
- Phase 5-6: 2-3 weeks
- Phase 7-9: 1-2 weeks

**Total Estimated Time: 7-11 weeks**

## Notes
- Many features claimed as "Done" in the PRD are not implemented
- Current codebase is a solid foundation but needs significant expansion
- Focus on core monetization features (subscriptions) first
- Web3 features should be implemented after core platform is stable
