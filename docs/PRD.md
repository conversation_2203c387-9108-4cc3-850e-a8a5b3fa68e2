# BonKai: Technical Product Requirements Document

## Executive Summary

BonKai is an AI-powered chatbot platform integrating advanced AI agents, subscription-based access, and cross-platform support (web and Telegram). It leverages xAI Grok models for chat, reasoning, and image generation. This PRD outlines precise technical specifications, focusing on scalable architecture, secure authentication, tiered monetization, and efficient AI routing. Gamification elements (e.g., rewards for usage milestones) are integrated to enhance user engagement but are not detailed in core implementation here.

To drive success in web3, a dedicated thesis on Bonk, AI, and BonkAI integration is added, emphasizing Solana ecosystem leverage, meme coin dynamics, and AI utilities for community-driven growth.

## 1. System Architecture

### Technology Stack
- **Frontend**: Next.js 15 (App Router, Partial Prerendering experimental)
- **Backend/Database**: PostgreSQL with Drizzle ORM; Redis for rate limiting
- **Authentication**: NextAuth.js v5 with subscription gating
- **Package Manager**: pnpm
- **AI Integration**: Vercel AI SDK v5 with xAI Grok models (Grok-2-Vision-1212, Grok-3-Mini-Beta, Grok-2-1212, <PERSON>rok-2-Image)
- **File Storage**: Vercel Blob
- **Payments**: Stripe (fiat); expand to crypto (e.g., Solana/Bonk) in web3 phase
- **Styling**: Tailwind CSS + shadcn/ui
- **Code Quality**: Biome (linting/formatting)
- **Testing**: Playwright (E2E)
- **Bot Framework**: grammY (Telegram)
- **Web3**: WalletConnect (Solana-compatible), potential Solana SDK for token interactions

### Project Structure
```
bonkai/
├── app/
│   ├── (auth)/          # Auth routes/API
│   ├── (chat)/          # Chat interface/API
│   ├── layout.tsx       # Root layout/providers
│   └── globals.css      # Styles
├── artifacts/           # Artifact handlers (code/image/sheet/text)
├── components/          # UI components (shadcn/ui + chat-specific + web3 wallet)
├── lib/
│   ├── ai/              # AI providers/models
│   ├── db/              # Schema/migrations
│   ├── payments/        # Stripe + crypto utilities
│   └── utils.ts         # Helpers
├── hooks/               # React hooks (add web3 hooks)
├── tests/               # Playwright tests
└── docs/                # Documentation
```

## 2. Subscription System

### 2.1 Tiers
Define in `lib/types.ts`:

```typescript
export enum SubscriptionTier { FREE = 'FREE', BRONZE = 'BRONZE', SILVER = 'SILVER', DIAMOND = 'DIAMOND' }

export interface TierLimits { name: string; price: number; tokensPerMonth: number; features: string[]; modelAccess: string[]; }

export const subscriptionTiers: Record<SubscriptionTier, TierLimits> = {
  [SubscriptionTier.FREE]: { name: 'Free', price: 0, tokensPerMonth: 10000, features: ['Basic chat', 'Limited history'], modelAccess: ['chat-model'] },
  [SubscriptionTier.BRONZE]: { name: 'Bronze', price: 20, tokensPerMonth: 100000, features: ['Chat history', 'Basic artifacts'], modelAccess: ['chat-model'] },
  [SubscriptionTier.SILVER]: { name: 'Silver', price: 50, tokensPerMonth: 500000, features: ['Reasoning models', 'Image generation', 'Advanced artifacts'], modelAccess: ['chat-model', 'chat-model-reasoning', 'image-model'] },
  [SubscriptionTier.DIAMOND]: { name: 'Diamond', price: 100, tokensPerMonth: 2000000, features: ['All models', 'Priority support'], modelAccess: ['chat-model', 'chat-model-reasoning', 'image-model', 'artifact-model'] }
};
```

### 2.2 Payments
In `lib/payments/stripe.ts`:

```typescript
import Stripe from 'stripe';

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, { apiVersion: '2023-10-16' });

export const createSubscription = async (customerId: string, priceId: string) => stripe.subscriptions.create({
  customer: customerId,
  items: [{ price: priceId }],
  payment_behavior: 'default_incomplete',
  payment_settings: { save_default_payment_method: 'on_subscription' },
  expand: ['latest_invoice.payment_intent']
});

export const priceIds = { [SubscriptionTier.BRONZE]: process.env.STRIPE_BRONZE_PRICE_ID!, /* ... */ };
```

## 3. Authentication

### 3.1 Setup
In `app/(auth)/auth.ts`:

```typescript
import NextAuth from 'next-auth';
import Credentials from 'next-auth/providers/credentials';
import { compare } from 'bcryptjs';
import { getUser, createGuestUser } from '@/lib/db/queries';

export const { handlers, auth, signIn, signOut } = NextAuth({
  pages: { signIn: '/login', newUser: '/' },
  providers: [
    Credentials({
      credentials: { email: {}, password: {} },
      async authorize(credentials) {
        const user = await getUser(credentials.email);
        if (!user || !await compare(credentials.password, user.password)) return null;
        return { ...user, type: 'regular' };
      }
    }),
    Credentials({ id: 'guest', credentials: {}, async authorize() { return { ...await createGuestUser(), type: 'guest' }; } })
  ],
  callbacks: {
    session({ session, token }) { /* add subscriptionTier, status */ },
    jwt({ token, user }) { /* add user data */ }
  }
});
```

### 3.2 Middleware Gating in `middleware.ts`:

```typescript
import { auth } from '@/app/(auth)/auth';
import { NextResponse, NextRequest } from 'next/server';

export async function subscriptionGateMiddleware(req: NextRequest, requiredTier: SubscriptionTier) {
  const session = await auth();
  if (!session?.user?.id) return NextResponse.redirect(new URL('/login', req.url));
  if (tierHierarchy[session.user.subscriptionTier] < tierHierarchy[requiredTier]) return NextResponse.json({ error: 'Upgrade required' }, { status: 403 });
  return null;
}

// Apply to routes, e.g., /api/ai/premium requires SILVER
```

## 4. AI Agent

### 4.1 Configuration
In `lib/ai/providers.ts`:

```typescript
import { customProvider, extractReasoningMiddleware, wrapLanguageModel } from 'ai';
import { xai } from '@ai-sdk/xai';

export const bonkaiProvider = customProvider({
  languageModels: {
    'chat-model': xai('Grok-2-1212'),
    'chat-model-reasoning': wrapLanguageModel({ model: xai('Grok-3-Mini-Beta'), middleware: extractReasoningMiddleware({ tagName: 'think' }) }),
    'title-model': xai('Grok-2-1212'),
    'artifact-model': xai('Grok-2-1212')
  },
  imageModels: { 'image-model': xai.imageModel('Grok-2-Image') }
});

// Tier models
export const models = {
  bronze: { chat: 'chat-model', reasoning: null, image: null },
  silver: { chat: 'chat-model', reasoning: 'chat-model-reasoning', image: 'image-model' },
  diamond: { ...silver, artifact: 'artifact-model' }
};
```

### 4.2 Task Routing
In `lib/ai/models.ts`:

```typescript
export interface ChatModel { id: string; name: string; description: string; tier: SubscriptionTier; }

export const chatModels = [
  { id: 'chat-model', name: 'Grok-2 Vision-1212', description: 'All-purpose chat with vision', tier: 'bronze' },
  { id: 'chat-model-reasoning', name: 'Grok-3 Mini-Beta Reasoning', description: 'Step-by-step reasoning', tier: 'silver' }
];

export function getModelForTask(complexity: 'simple' | 'complex', userTier: SubscriptionTier, taskType: 'text' | 'image' | 'reasoning') {
  if (userTier < required for task) throw Error('Tier restricted');
  return taskType === 'reasoning' ? 'chat-model-reasoning' : taskType === 'image' ? 'image-model' : 'chat-model';
}
```

### 4.3 Chat API
In `app/(chat)/api/chat/route.ts`:

```typescript
import { bonkaiProvider } from '@/lib/ai/providers';
import { streamText, convertToCoreMessages } from 'ai';

export async function POST(req: Request) {
  const { messages, selectedModel } = await req.json();
  const session = await auth();
  if (!session) return new Response('Unauthorized', { status: 401 });
  const allowed = getModelForTask('simple', session.user.subscriptionTier, selectedModel.includes('reasoning') ? 'reasoning' : 'text');
  if (!allowed) return new Response('Tier denied', { status: 403 });
  const result = await streamText({ model: bonkaiProvider(allowed), messages: convertToCoreMessages(messages), ... });
  return result.toDataStreamResponse();
  // On finish, log usage
}
```

## 5. Telegram Integration

### 5.1 Bot Setup
```typescript
import { Bot } from "grammy";

const bot = new Bot(process.env.BOT_TOKEN);

bot.use(async (ctx, next) => { /* fetch user/tier, set session */ });
```

### 5.2 Commands
- `/chat [prompt]`: Generate response; tier-checked.
- `/image [prompt]`: Generate image (Silver+); reply with photo.
- `/status`: Show tier/usage.

### 5.3 Sync
Use WebSocket (port 3001) for real-time chat sync between web/Telegram; save messages to DB.

## 6. Database Schema
In `lib/db/schema.ts`:

```typescript
export const user = pgTable('User', { id: uuid().primaryKey(), email: varchar(), subscriptionTier: varchar({ enum: ['FREE', ...] }).default('FREE'), ... });

export const chat = pgTable('Chat', { id: uuid().primaryKey(), userId: references(user.id), ... });

export const message = pgTable('Message', { id: uuid().primaryKey(), chatId: references(chat.id), role: varchar(), parts: json(), ... });

export const usageLog = pgTable('UsageLog', { userId: references(user.id), model: varchar(), tokensUsed: integer(), ... });
```

Optimize with indexes, connection pooling.

## 7. Frontend
### 7.1 Components
Focus on chat UI; expand WalletConnect for Solana/Bonk integrations.

### 7.2 State
Use Zustand with persist for session/wallet.

## 8. Storage
Use Vercel Blob for uploads; check tier limits before save.

## 9. Security
- Rate limiting: Redis sliding window per tier (50-1000 req/h).
- Verify signatures for sensitive actions.

## 10. Roadmap
- Phase 1 (W1-2): Auth, subscriptions, DB, Stripe. **Done**
- Phase 2 (W3-4): AI integration, model routing, usage. **Done**
- Phase 3 (W5-6): Artifacts, chat UI, storage. **Done**
- Phase 4 (W7-8): Telegram bot, sync, advanced AI. **Done**
- Phase 5 (W9-10): Audit, test, deploy. **Done**
- Phase 6 (W11-12): Web3 integrations (Bonk payments, token rewards, Solana deployments).

## 11. Costs
- AI: Variable (xAI tokens).
- Infra: $100-300/month (Vercel, DB, Redis).
- Payments: Stripe fees (2.9% + $0.30/tx); minimal for crypto.

## 12. Advantages
1. Scalable Next.js + Vercel AI.
2. Secure tiered access.
3. Efficient DB/AI routing.
4. Cross-platform sync.
5. Usage tracking for monetization.
6. Type-safe, testable code.

## 13. Bonk, AI, and BonkAI Thesis for Web3 Success

### 13.1 Introduction
BonKai positions itself at the intersection of meme coin culture (exemplified by Bonk), artificial intelligence, and web3 decentralization on Solana. Bonk ($BONK), the first dog-themed meme coin on Solana, was launched in December 2022 with 50% airdropped to the community, achieving a market cap of ~$1.9B and serving as Solana's "social layer" with integrations across DEXs, dApps, and protocols. AI in crypto is booming, with projects blending meme narratives and utilities like image generation, agents, and analytics. BonkAI ($BONKAI) variants exist: a Solana-based utility project for AI agents and data processing (launched on letsbonk.fun, enabling custom AI creation), and Ethereum-based versions for AI image/3D tools. BonKai builds on this by fusing Grok AI models with Bonk-themed gamification, positioning as the "BonkAI" hub for Solana degens.

### 13.2 Market Opportunity
- **Bonk Ecosystem**: Bonk drives Solana's meme meta, with viral launches (e.g., $HOSICO, $BONKY) and partnerships (e.g., Gotbit for market making, reaching billions in FDV). Community tokens like Bonk fuel liquidity and engagement, with tools like Pump.fun/Bonk.fun enabling fair launches.
- **AI in Web3**: AI agents (e.g., $NYLA as "Bonk’s AI runner/Venmo of Web3", BonksmashAI for degen tools) are rising, offering alpha extraction, risk scoring, and onboarding non-crypto users. Solana's speed suits AI (e.g., Klon AI agents). Market: Meme coins >$50B sector; AI crypto projects surging in bull runs.
- **BonkAI Niche**: Existing $BONKAI tokens (MC ~low millions) focus on AI utilities but lack deep Grok integration or cross-platform (web/Telegram) reach. BonKai fills this with subscription-gated AI, themed around Bonk memes.

### 13.3 Unique Value Proposition
BonKai as "BonkAI Platform":
- **AI-Powered Meme Utilities**: Grok models for Bonk-themed image gen (e.g., dog memes), reasoning (trade alpha), artifacts (NFT mocks).
- **Web3 Integrations**: Accept $BONK for subs/rewards; WalletConnect for Solana; launch $BONKAI-inspired token for governance/staking.
- **Community Focus**: PvE gamification (earn Bonk rewards); Telegram bots for degen chats; sync with Solana dApps.
- **Scalability on Solana**: Low fees, fast txns for AI queries; potential Bonk.fun launch for visibility.

### 13.4 Growth Strategy
1. **Ecosystem Partnerships**: Collaborate with Bonk (e.g., airdrops), Raydium, Gotbit for liquidity/MC growth.
2. **Tokenomics**: Introduce utility token (e.g., $BONKAI variant) for AI access, staking yields in $BONK, burn mechanics.
3. **Marketing**: Viral X/TikTok shills (animal/AI narratives); hackathons for AI agents.
4. **Onboarding**: Free tier for Solana newbies; AI tools for wallet analysis/risk to attract degens.
5. **Expansion**: Integrate with Bonk eco projects (e.g., $NYLA synergies); CEX listings for visibility.
6. **Metrics for Success**: Aim 100K users Q1 post-launch; $10M TVL in rewards; top Solana AI by volume.

### 13.5 Risks and Mitigations
- **Volatility**: Meme coin dumps; mitigate via utility focus, locked LP.
- **Competition**: Other AI (e.g., $NYLA, BonksmashAI); differentiate with Grok exclusivity, Bonk branding.
- **Regulatory**: Crypto subs; use Stripe hybrid, monitor Solana compliance.
- **Adoption**: Low web3 AI awareness; counter with gamified tutorials, Bonk community airdrops.

This thesis transforms BonKai into a web3 powerhouse, capitalizing on Bonk's 88B+ supply/community and AI trends for sustained growth.