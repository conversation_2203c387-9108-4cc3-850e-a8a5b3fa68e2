@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --foreground-rgb: 0, 0, 0;
    --background-start-rgb: 214, 219, 220;
    --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
    :root {
        --foreground-rgb: 255, 255, 255;
        --background-start-rgb: 0, 0, 0;
        --background-end-rgb: 0, 0, 0;
    }
}

@layer utilities {
    .text-balance {
        text-wrap: balance;
    }
}

@layer base {
    :root {
        --background: oklch(0.9383 0.0042 236.4993);
        --foreground: oklch(0.3211 0 0);
        --card: oklch(1.0000 0 0);
        --card-foreground: oklch(0.3211 0 0);
        --popover: oklch(1.0000 0 0);
        --popover-foreground: oklch(0.3211 0 0);
        --primary: oklch(0.6397 0.1720 36.4421);
        --primary-foreground: oklch(1.0000 0 0);
        --secondary: oklch(0.9670 0.0029 264.5419);
        --secondary-foreground: oklch(0.4461 0.0263 256.8018);
        --muted: oklch(0.9846 0.0017 247.8389);
        --muted-foreground: oklch(0.5510 0.0234 264.3637);
        --accent: oklch(0.9119 0.0222 243.8174);
        --accent-foreground: oklch(0.3791 0.1378 265.5222);
        --destructive: oklch(0.6368 0.2078 25.3313);
        --destructive-foreground: oklch(1.0000 0 0);
        --border: oklch(0.9022 0.0052 247.8822);
        --input: oklch(0.9700 0.0029 264.5420);
        --ring: oklch(0.6397 0.1720 36.4421);
        --chart-1: oklch(0.7156 0.0605 248.6845);
        --chart-2: oklch(0.7875 0.0917 35.9616);
        --chart-3: oklch(0.5778 0.0759 254.1573);
        --chart-4: oklch(0.5016 0.0849 259.4902);
        --chart-5: oklch(0.4241 0.0952 264.0306);
        --radius: 0.75rem;
        --sidebar-background: 0 0% 98%;
        --sidebar-foreground: oklch(0.3211 0 0);
        --sidebar-primary: oklch(0.6397 0.1720 36.4421);
        --sidebar-primary-foreground: oklch(1.0000 0 0);
        --sidebar-accent: oklch(0.9119 0.0222 243.8174);
        --sidebar-accent-foreground: oklch(0.3791 0.1378 265.5222);
        --sidebar-border: oklch(0.9276 0.0058 264.5313);
        --sidebar-ring: oklch(0.6397 0.1720 36.4421);
        --sidebar: oklch(0.9030 0.0046 258.3257);
        --font-sans: Inter, sans-serif;
        --font-serif: Source Serif 4, serif;
        --font-mono: JetBrains Mono, monospace;
        --shadow-color: hsl(0 0% 0%);
        --shadow-opacity: 0.1;
        --shadow-blur: 3px;
        --shadow-spread: 0px;
        --shadow-offset-x: 0px;
        --shadow-offset-y: 1px;
        --letter-spacing: 0em;
        --spacing: 0.25rem;
        --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
        --shadow-xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
        --shadow-sm: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
        --shadow: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
        --shadow-md: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
        --shadow-lg: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
        --shadow-xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
        --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.25);
        --tracking-normal: 0em;
    }
    .dark {
        --background: oklch(0.2598 0.0306 262.6666);
        --foreground: oklch(0.9219 0 0);
        --card: oklch(0.3106 0.0301 268.6365);
        --card-foreground: oklch(0.9219 0 0);
        --popover: oklch(0.2900 0.0249 268.3986);
        --popover-foreground: oklch(0.9219 0 0);
        --primary: oklch(0.6397 0.1720 36.4421);
        --primary-foreground: oklch(1.0000 0 0);
        --secondary: oklch(0.3095 0.0266 266.7132);
        --secondary-foreground: oklch(0.9219 0 0);
        --muted: oklch(0.3095 0.0266 266.7132);
        --muted-foreground: oklch(0.7155 0 0);
        --accent: oklch(0.3380 0.0589 267.5867);
        --accent-foreground: oklch(0.8823 0.0571 254.1284);
        --destructive: oklch(0.6368 0.2078 25.3313);
        --destructive-foreground: oklch(1.0000 0 0);
        --border: oklch(0.3843 0.0301 269.7337);
        --input: oklch(0.3843 0.0301 269.7337);
        --ring: oklch(0.6397 0.1720 36.4421);
        --chart-1: oklch(0.7156 0.0605 248.6845);
        --chart-2: oklch(0.7693 0.0876 34.1875);
        --chart-3: oklch(0.5778 0.0759 254.1573);
        --chart-4: oklch(0.5016 0.0849 259.4902);
        --chart-5: oklch(0.4241 0.0952 264.0306);
        --sidebar-background: 240 5.9% 10%;
        --sidebar-foreground: oklch(0.9219 0 0);
        --sidebar-primary: oklch(0.6397 0.1720 36.4421);
        --sidebar-primary-foreground: oklch(1.0000 0 0);
        --sidebar-accent: oklch(0.3380 0.0589 267.5867);
        --sidebar-accent-foreground: oklch(0.8823 0.0571 254.1284);
        --sidebar-border: oklch(0.3843 0.0301 269.7337);
        --sidebar-ring: oklch(0.6397 0.1720 36.4421);
        --radius: 0.75rem;
        --sidebar: oklch(0.3100 0.0283 267.7408);
        --font-sans: Inter, sans-serif;
        --font-serif: Source Serif 4, serif;
        --font-mono: JetBrains Mono, monospace;
        --shadow-color: hsl(0 0% 0%);
        --shadow-opacity: 0.1;
        --shadow-blur: 3px;
        --shadow-spread: 0px;
        --shadow-offset-x: 0px;
        --shadow-offset-y: 1px;
        --letter-spacing: 0em;
        --spacing: 0.25rem;
        --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
        --shadow-xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
        --shadow-sm: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
        --shadow: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
        --shadow-md: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
        --shadow-lg: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
        --shadow-xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
        --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.25);
    }
  .theme {
        --font-sans: Inter, sans-serif;
        --font-mono: JetBrains Mono, monospace;
        --font-serif: Source Serif 4, serif;
        --radius: 0.75rem;
        --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
        --tracking-tight: calc(var(--tracking-normal) - 0.025em);
        --tracking-wide: calc(var(--tracking-normal) + 0.025em);
        --tracking-wider: calc(var(--tracking-normal) + 0.05em);
        --tracking-widest: calc(var(--tracking-normal) + 0.1em);
    }
  body {
    letter-spacing: var(--tracking-normal);
    }
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}

.skeleton {
    * {
        pointer-events: none !important;
    }

    *[class^="text-"] {
        color: transparent;
        @apply rounded-md bg-foreground/20 select-none animate-pulse;
    }

    .skeleton-bg {
        @apply bg-foreground/10;
    }

    .skeleton-div {
        @apply bg-foreground/20 animate-pulse;
    }
}

.ProseMirror {
    outline: none;
}

.cm-editor,
.cm-gutters {
    @apply bg-background dark:bg-zinc-800 outline-none selection:bg-zinc-900 !important;
}

.ͼo.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground,
.ͼo.cm-selectionBackground,
.ͼo.cm-content::selection {
    @apply bg-zinc-200 dark:bg-zinc-900 !important;
}

.cm-activeLine,
.cm-activeLineGutter {
    @apply bg-transparent !important;
}

.cm-activeLine {
    @apply rounded-r-sm !important;
}

.cm-lineNumbers {
    @apply min-w-7;
}

.cm-foldGutter {
    @apply min-w-3;
}

.cm-lineNumbers .cm-activeLineGutter {
    @apply rounded-l-sm !important;
}

.suggestion-highlight {
    @apply bg-blue-200 hover:bg-blue-300 dark:hover:bg-blue-400/50 dark:text-blue-50 dark:bg-blue-500/40;
}