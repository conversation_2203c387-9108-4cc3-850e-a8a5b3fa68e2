# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
# Development
pnpm dev                    # Start Next.js dev server with Turbo
pnpm build                  # Run DB migration then build application
pnpm start                  # Start production server

# Linting & Formatting
pnpm lint                   # Run Next.js ESLint + Biome lint with --write --unsafe
pnpm lint:fix               # Run linting with auto-fix
pnpm format                 # Format code with Biome

# Database Operations
pnpm db:generate            # Generate Drizzle migrations from schema
pnpm db:migrate             # Run pending migrations (uses npx tsx lib/db/migrate.ts)
pnpm db:studio              # Open Drizzle Studio for database inspection
pnpm db:push                # Push schema changes directly to database
pnpm db:pull                # Pull schema from database
pnpm db:check               # Check migration consistency
pnpm db:up                  # Apply pending migrations

# Testing
pnpm test                   # Run Playwright tests (sets PLAYWRIGHT=True env var)
```

## Project Architecture

This is an AI chatbot application built with Next.js 15 and the Vercel AI SDK, featuring real-time chat, document generation (artifacts), and user authentication.

### Core Technologies
- **Framework**: Next.js 15 with App Router and experimental PPR (Partial Prerendering)
- **AI Integration**: Vercel AI SDK v5 beta with OpenRouter (configurable models via FAST/SMART/AGENT env vars)
- **Database**: PostgreSQL with Drizzle ORM for schema management
- **Authentication**: NextAuth.js v5 beta with credential-based auth
- **Styling**: Tailwind CSS with shadcn/ui components
- **Code Quality**: Biome for linting/formatting, Playwright for E2E testing
- **Package Manager**: pnpm with lockfile v9.12.3

### Directory Structure
- `app/(auth)/` - Authentication routes and configuration (login, register)
- `app/(chat)/` - Main chat interface and API routes
- `artifacts/` - Document generation system (code, image, sheet, text artifacts)
- `components/` - Reusable UI components and chat-specific components
- `lib/` - Core utilities, database schema, AI providers, and tools
- `tests/` - Playwright test suites for E2E and route testing

### Key Features

**Chat System**
- Real-time streaming chat with message persistence
- Support for reasoning models and tool calling
- Chat visibility controls (public/private)
- Message voting system for feedback

**Artifacts System**
- Dynamic document generation for code, text, images, and spreadsheets
- Live editing with suggestion system
- Document versioning and collaborative editing
- Integrated code execution environment (Pyodide for Python)

**Database Schema**
- User management with email/password authentication
- Chat persistence with message parts and attachments
- Document storage with artifact kind classification
- Suggestion system for document improvements
- Stream management for resumable conversations

### AI Integration
- Tool calling system with weather, document creation/update, and suggestions
- Model selection between standard chat and reasoning models
- Geographic context integration via Vercel Functions
- Telemetry and observability for production monitoring

### Development Notes
- Uses experimental features: PPR, activeTools, transform (smoothStream)
- Redis integration for resumable streams (optional)
- Vercel Blob storage for file uploads
- Rate limiting based on user type and message count
- Environment-based configuration with `.env.local`