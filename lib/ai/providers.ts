import {
  customProvider,
  extractReasoningMiddleware,
  wrapLanguageModel,
} from 'ai';
import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import {
  artifactModel,
  chatModel,
  reasoningModel,
  titleModel,
} from './models.test';
import { isTestEnvironment } from '../constants';

// Configure OpenRouter client
const openrouterClient = createOpenRouter({
  apiKey: process.env.OPENROUTER_API_KEY || '',
});

// Log model configuration for debugging
console.log('🤖 AI Model Configuration:');
console.log('  FAST model:', process.env.FAST || 'anthropic/claude-3.5-sonnet (default)');
console.log('  SMART model:', process.env.SMART || 'anthropic/claude-3.5-sonnet (default)');
console.log('  AGENT model:', process.env.AGENT || 'anthropic/claude-3.5-sonnet (default)');
console.log('  OpenRouter API Key:', process.env.OPENROUTER_API_KEY ? '✅ Set' : '❌ Missing');

export const myProvider = isTestEnvironment
  ? customProvider({
      languageModels: {
        'chat-model': chatModel,
        'chat-model-reasoning': reasoningModel,
        'title-model': titleModel,
        'artifact-model': artifactModel,
      },
    })
  : customProvider({
      languageModels: {
        'chat-model': openrouterClient(process.env.FAST || 'anthropic/claude-3.5-sonnet'),
        'chat-model-reasoning': wrapLanguageModel({
          model: openrouterClient(process.env.SMART || 'anthropic/claude-3.5-sonnet'),
          middleware: extractReasoningMiddleware({ tagName: 'think' }),
        }),
        'title-model': openrouterClient(process.env.FAST || 'anthropic/claude-3.5-sonnet'),
        'artifact-model': openrouterClient(process.env.AGENT || 'anthropic/claude-3.5-sonnet'),
      },
    });
