export const DEFAULT_CHAT_MODEL: string = 'chat-model';

export interface ChatModel {
  id: string;
  name: string;
  description: string;
}

export const chatModels: Array<ChatModel> = [
  {
    id: 'chat-model',
    name: 'Fast Model',
    description: 'Quick responses for general chat (configurable via FAST env var)',
  },
  {
    id: 'chat-model-reasoning',
    name: 'Smart Model',
    description: 'Advanced reasoning and complex tasks (configurable via SMART env var)',
  },
];
