import { createDocumentHandler } from '@/lib/artifacts/server';

// TODO: Image generation temporarily disabled - OpenRouter doesn't support image generation
// Will need to integrate with a separate image generation service (e.g., DALL-E, Midjourney, etc.)
export const imageDocumentHandler = createDocumentHandler<'image'>({
  kind: 'image',
  onCreateDocument: async ({ title, dataStream }) => {
    // Placeholder implementation - image generation disabled
    const draftContent = '';

    dataStream.write({
      type: 'data-imageDelta',
      data: 'placeholder-image-disabled',
      transient: true,
    });

    return draftContent;
  },
  onUpdateDocument: async ({ description, dataStream }) => {
    // Placeholder implementation - image generation disabled
    const draftContent = '';

    dataStream.write({
      type: 'data-imageDelta',
      data: 'placeholder-image-disabled',
      transient: true,
    });

    return draftContent;
  },
});
